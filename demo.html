<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة التطبيق - React + Node.js</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .status {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .status.error {
            background: #fff5f5;
            border-color: #fed7d7;
            color: #c53030;
        }

        .status.success {
            background: #f0fff4;
            border-color: #9ae6b4;
            color: #2f855a;
        }

        .features {
            text-align: right;
            margin: 30px 0;
        }

        .features h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            background: #edf2f7;
            margin: 8px 0;
            padding: 12px;
            border-radius: 8px;
            border-right: 4px solid #667eea;
        }

        .steps {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .steps h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }

        .steps ol {
            padding-right: 20px;
        }

        .steps li {
            margin: 10px 0;
            line-height: 1.6;
        }

        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }

        .button:hover {
            transform: translateY(-2px);
        }

        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            text-align: left;
            direction: ltr;
        }

        .emoji {
            font-size: 1.5em;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 تطبيق React + Node.js</h1>
            <p class="subtitle">معاينة المشروع قبل التثبيت</p>
        </div>

        <div class="status error" id="status">
            <h3>⚠️ Node.js غير مثبت</h3>
            <p>يجب تثبيت Node.js أولاً لتشغيل التطبيق</p>
        </div>

        <div class="features">
            <h3>🌟 الميزات المتاحة في التطبيق:</h3>
            <ul>
                <li>✅ واجهة React حديثة مع دعم العربية</li>
                <li>✅ خادم Node.js مع Express</li>
                <li>✅ API endpoints جاهزة للاستخدام</li>
                <li>✅ اتصال بين الواجهة الأمامية والخلفية</li>
                <li>✅ مكون اختبار API تفاعلي</li>
                <li>✅ إعدادات التطوير مع Hot Reload</li>
            </ul>
        </div>

        <div class="steps">
            <h3>📋 خطوات التثبيت:</h3>
            <ol>
                <li>اذهب إلى <strong>https://nodejs.org/</strong></li>
                <li>حمل النسخة LTS (الخضراء)</li>
                <li>ثبت Node.js وتأكد من تحديد "Add to PATH"</li>
                <li>أعد تشغيل الكمبيوتر</li>
                <li>افتح PowerShell جديد في مجلد المشروع</li>
                <li>اكتب الأوامر التالية:</li>
            </ol>
            
            <div class="code">
npm run install-all<br>
npm run dev
            </div>
        </div>

        <div class="status" id="demo-status">
            <h3>🎯 بعد التثبيت ستحصل على:</h3>
            <p><strong>الواجهة الأمامية:</strong> http://localhost:3000</p>
            <p><strong>الخادم الخلفي:</strong> http://localhost:5000</p>
            <p><strong>API:</strong> http://localhost:5000/api</p>
        </div>

        <button class="button" onclick="openNodeJS()">
            🌐 فتح موقع Node.js
        </button>
        
        <button class="button" onclick="showFiles()">
            📁 عرض ملفات المشروع
        </button>

        <div id="file-list" style="display: none; margin-top: 20px; text-align: right;">
            <h3>📂 ملفات المشروع المنشأة:</h3>
            <div class="code" style="text-align: right; direction: rtl;">
                ✅ README.md - معلومات المشروع<br>
                ✅ SETUP.md - دليل التشغيل<br>
                ✅ TROUBLESHOOTING.md - حل المشاكل<br>
                ✅ frontend/ - تطبيق React<br>
                ✅ backend/ - خادم Node.js<br>
                ✅ package.json - إعدادات المشروع<br>
            </div>
        </div>
    </div>

    <script>
        function openNodeJS() {
            window.open('https://nodejs.org/', '_blank');
        }

        function showFiles() {
            const fileList = document.getElementById('file-list');
            if (fileList.style.display === 'none') {
                fileList.style.display = 'block';
            } else {
                fileList.style.display = 'none';
            }
        }

        // محاولة اكتشاف إذا كان Node.js مثبت
        function checkNodeJS() {
            // هذا لن يعمل في المتصفح، لكنه للعرض فقط
            const status = document.getElementById('status');
            status.innerHTML = `
                <h3>ℹ️ حالة Node.js</h3>
                <p>لا يمكن التحقق من المتصفح - يرجى فتح PowerShell والتحقق يدوياً</p>
                <div class="code">node --version<br>npm --version</div>
            `;
        }

        // تشغيل الفحص عند تحميل الصفحة
        window.onload = checkNodeJS;
    </script>
</body>
</html>
