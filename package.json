{"name": "react-nodejs-app", "version": "1.0.0", "description": "تطبيق ويب باستخدام React و Node.js", "scripts": {"install-all": "npm install && cd frontend && npm install && cd ../backend && npm install", "dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["react", "nodejs", "express", "fullstack"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^8.2.2"}}