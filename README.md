# تطبيق React + Node.js

هذا مشروع تطبيق ويب يستخدم React للواجهة الأمامية و Node.js للخادم الخلفي.

## متطلبات التشغيل

1. **تثبيت Node.js:**
   - قم بتحميل Node.js من الموقع الرسمي: https://nodejs.org/
   - اختر النسخة LTS (Long Term Support)
   - قم بتثبيتها واتبع التعليمات

2. **التحقق من التثبيت:**
   ```bash
   node --version
   npm --version
   ```

## هيكل المشروع

```
├── frontend/          # تطبيق React
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/           # خادم Node.js
│   ├── src/
│   └── package.json
└── README.md
```

## خطوات التشغيل

### 1. إعداد الواجهة الأمامية (React)
```bash
cd frontend
npm install
npm run dev
```

### 2. إعد<PERSON> الخادم الخلفي (Node.js)
```bash
cd backend
npm install
npm start
```

## الخطوات التالية

بعد تثبيت Node.js، قم بتشغيل الأوامر التالية لإنشاء المشروع:

```bash
# إنشاء تطبيق React
npm create vite@latest frontend -- --template react

# إنشاء مجلد الخادم
mkdir backend
cd backend
npm init -y
npm install express cors dotenv
npm install -D nodemon
```
