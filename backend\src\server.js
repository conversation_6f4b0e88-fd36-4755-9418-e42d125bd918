import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'

// تحميل متغيرات البيئة
dotenv.config()

const app = express()
const PORT = process.env.PORT || 5000

// Middleware
app.use(cors())
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// Routes
app.get('/api/hello', (req, res) => {
  res.json({
    message: 'مرحباً من الخادم الخلفي! 🎉',
    timestamp: new Date().toISOString(),
    status: 'success'
  })
})

app.get('/api/status', (req, res) => {
  res.json({
    status: 'الخادم يعمل بشكل طبيعي',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version
  })
})

// مسار للحصول على بيانات تجريبية
app.get('/api/data', (req, res) => {
  const sampleData = [
    { id: 1, name: 'محمد', email: '<EMAIL>' },
    { id: 2, name: 'فاطمة', email: '<EMAIL>' },
    { id: 3, name: 'أحمد', email: '<EMAIL>' }
  ]
  
  res.json({
    data: sampleData,
    count: sampleData.length,
    message: 'تم جلب البيانات بنجاح'
  })
})

// مسار POST تجريبي
app.post('/api/users', (req, res) => {
  const { name, email } = req.body
  
  if (!name || !email) {
    return res.status(400).json({
      error: 'الاسم والبريد الإلكتروني مطلوبان',
      message: 'يرجى إدخال جميع البيانات المطلوبة'
    })
  }
  
  // محاكاة إنشاء مستخدم جديد
  const newUser = {
    id: Date.now(),
    name,
    email,
    createdAt: new Date().toISOString()
  }
  
  res.status(201).json({
    message: 'تم إنشاء المستخدم بنجاح',
    user: newUser
  })
})

// معالجة الأخطاء 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'المسار غير موجود',
    message: 'الصفحة التي تبحث عنها غير متاحة'
  })
})

// معالجة الأخطاء العامة
app.use((error, req, res, next) => {
  console.error('خطأ في الخادم:', error)
  res.status(500).json({
    error: 'خطأ داخلي في الخادم',
    message: 'حدث خطأ غير متوقع'
  })
})

// بدء تشغيل الخادم
app.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`)
  console.log(`📍 الرابط: http://localhost:${PORT}`)
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`)
})
