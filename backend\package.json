{"name": "backend", "version": "1.0.0", "description": "خادم Node.js للتطبيق", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "api"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}}