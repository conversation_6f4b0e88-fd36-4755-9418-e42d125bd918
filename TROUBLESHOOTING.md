# 🔧 حل مشاكل التثبيت والتشغيل

## المشكلة: "npm is not recognized"

### الحل الأول: تثبيت Node.js يدوياً

1. **تحميل Node.js:**
   - اذهب إلى: https://nodejs.org/
   - اضغط على "Download Node.js (LTS)" - النسخة الخضراء
   - احفظ الملف على سطح المكتب

2. **تثبيت Node.js:**
   - اضغط مرتين على الملف المحمل (مثل: node-v20.x.x-x64.msi)
   - اتبع خطوات التثبيت:
     - اضغط "Next"
     - اقبل الشروط واضغط "Next"
     - **مهم جداً**: تأكد من تحديد "Add to PATH" ✅
     - اضغط "Next" ثم "Install"
   - انتظر حتى ينتهي التثبيت

3. **إعادة تشغيل الكمبيوتر:**
   - هذا مهم جداً لتحديث متغيرات النظام

### الحل الثاني: استخدام Microsoft Store

1. افتح Microsoft Store
2. ابحث عن "Node.js"
3. اضغط "Install"

### الحل الثالث: تثبيت عبر Winget

افتح PowerShell كمدير واكتب:
```powershell
winget install OpenJS.NodeJS
```

## التحقق من التثبيت

بعد التثبيت وإعادة التشغيل، افتح PowerShell جديد واكتب:

```powershell
node --version
npm --version
```

يجب أن ترى أرقام الإصدارات (مثل: v20.x.x)

## مشاكل أخرى محتملة

### مشكلة: "Execution Policy"
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### مشكلة: "Permission Denied"
- شغل PowerShell كمدير (Run as Administrator)

### مشكلة: "Path not found"
أضف Node.js للـ PATH يدوياً:
1. اضغط Windows + R
2. اكتب: sysdm.cpl
3. اضغط "Environment Variables"
4. في "System Variables" ابحث عن "Path"
5. اضغط "Edit" ثم "New"
6. أضف: `C:\Program Files\nodejs`

## بديل: تشغيل بدون تثبيت

إذا لم ينجح التثبيت، يمكنك:

1. **تحميل Node.js Portable:**
   - اذهب إلى: https://nodejs.org/dist/
   - حمل النسخة المضغوطة (.zip)
   - فك الضغط في مجلد

2. **تشغيل مباشر:**
   ```cmd
   cd "path\to\extracted\nodejs"
   node.exe --version
   ```

## طلب المساعدة

إذا لم تنجح أي من الطرق:

1. أرسل لي screenshot من رسالة الخطأ
2. أخبرني نوع نظام التشغيل (Windows 10/11)
3. أخبرني إذا كنت تستخدم حساب مدير أم لا

## روابط مفيدة

- الموقع الرسمي: https://nodejs.org/
- دليل التثبيت: https://nodejs.org/en/download/
- حل المشاكل: https://docs.npmjs.com/troubleshooting
