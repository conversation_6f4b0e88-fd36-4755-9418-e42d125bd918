import { useState, useEffect } from 'react'
import './App.css'
import ApiTest from './components/ApiTest'

function App() {
  const [message, setMessage] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // استدعاء API من الخادم الخلفي
    fetch('/api/hello')
      .then(response => response.json())
      .then(data => {
        setMessage(data.message)
        setLoading(false)
      })
      .catch(error => {
        console.error('خطأ في الاتصال بالخادم:', error)
        setMessage('مرحباً من React! (الخادم الخلفي غير متصل)')
        setLoading(false)
      })
  }, [])

  return (
    <div className="App">
      <header className="App-header">
        <h1>🚀 تطبيق React + Node.js</h1>
        {loading ? (
          <p>جاري التحميل...</p>
        ) : (
          <p className="message">{message}</p>
        )}
        <div className="features">
          <h2>الميزات المتاحة:</h2>
          <ul>
            <li>✅ React مع Vite للتطوير السريع</li>
            <li>✅ Node.js Express للخادم الخلفي</li>
            <li>✅ اتصال بين الواجهة الأمامية والخلفية</li>
            <li>✅ دعم اللغة العربية</li>
          </ul>
        </div>

        <ApiTest />
      </header>
    </div>
  )
}

export default App
