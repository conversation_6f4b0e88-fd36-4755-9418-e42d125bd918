@echo off
echo ========================================
echo       تثبيت Node.js - دليل مفصل
echo ========================================
echo.
echo الخطوة 1: تحميل Node.js
echo ------------------------
echo 1. اذهب الى: https://nodejs.org/
echo 2. اضغط على الزر الاخضر "Download Node.js (LTS)"
echo 3. انتظر حتى ينتهي التحميل
echo.
echo الخطوة 2: تثبيت Node.js
echo ------------------------
echo 1. اضغط مرتين على الملف المحمل
echo 2. اضغط "Next" في كل خطوة
echo 3. تأكد من تحديد "Add to PATH"
echo 4. اضغط "Install"
echo 5. انتظر حتى ينتهي التثبيت
echo.
echo الخطوة 3: اعادة تشغيل الكمبيوتر
echo --------------------------------
echo بعد التثبيت، اعد تشغيل الكمبيوتر
echo.
echo الخطوة 4: تشغيل التطبيق
echo ------------------------
echo افتح PowerShell جديد واكتب:
echo   cd "C:\Users\<USER>\Desktop\New folder"
echo   node --version
echo   npm --version
echo   npm run install-all
echo   npm run dev
echo.
echo ========================================
pause
