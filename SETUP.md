# دليل إعداد وتشغيل التطبيق

## 📋 المتطلبات الأساسية

### 1. تثبيت Node.js
- اذهب إلى [nodejs.org](https://nodejs.org/)
- حمل النسخة LTS (الموصى بها)
- قم بتثبيتها واتبع التعليمات
- تأكد من التثبيت بتشغيل:
  ```bash
  node --version
  npm --version
  ```

## 🚀 خطوات التشغيل

### الطريقة الأولى: تشغيل سريع
```bash
# تثبيت جميع التبعيات
npm run install-all

# تشغيل الواجهة الأمامية والخلفية معاً
npm run dev
```

### الطريقة الثانية: تشغيل منفصل

#### 1. تشغيل الخادم الخلفي (Backend)
```bash
cd backend
npm install
npm run dev
```
الخادم سيعمل على: http://localhost:5000

#### 2. تشغيل الواجهة الأمامية (Frontend)
```bash
# في terminal جديد
cd frontend
npm install
npm run dev
```
التطبيق سيعمل على: http://localhost:3000

## 🔧 الأوامر المتاحة

### أوامر المشروع الرئيسي:
- `npm run install-all` - تثبيت جميع التبعيات
- `npm run dev` - تشغيل الواجهة الأمامية والخلفية معاً
- `npm run client` - تشغيل الواجهة الأمامية فقط
- `npm run server` - تشغيل الخادم الخلفي فقط
- `npm run build` - بناء التطبيق للإنتاج

### أوامر الواجهة الأمامية (frontend):
- `npm run dev` - تشغيل خادم التطوير
- `npm run build` - بناء التطبيق للإنتاج
- `npm run preview` - معاينة النسخة المبنية

### أوامر الخادم الخلفي (backend):
- `npm run dev` - تشغيل الخادم مع إعادة التحميل التلقائي
- `npm start` - تشغيل الخادم في وضع الإنتاج

## 🌐 نقاط النهاية المتاحة (API Endpoints)

- `GET /api/hello` - رسالة ترحيب
- `GET /api/status` - حالة الخادم
- `GET /api/data` - بيانات تجريبية
- `POST /api/users` - إنشاء مستخدم جديد

## 📁 هيكل المشروع

```
├── frontend/              # تطبيق React
│   ├── src/
│   │   ├── App.jsx       # المكون الرئيسي
│   │   ├── App.css       # تنسيقات التطبيق
│   │   ├── main.jsx      # نقطة الدخول
│   │   └── index.css     # التنسيقات العامة
│   ├── index.html        # ملف HTML الرئيسي
│   ├── package.json      # تبعيات الواجهة الأمامية
│   └── vite.config.js    # إعدادات Vite
├── backend/               # خادم Node.js
│   ├── src/
│   │   └── server.js     # الخادم الرئيسي
│   ├── package.json      # تبعيات الخادم الخلفي
│   └── .env              # متغيرات البيئة
├── package.json          # إعدادات المشروع الرئيسي
├── README.md             # معلومات المشروع
└── SETUP.md              # هذا الملف
```

## 🔍 استكشاف الأخطاء

### مشكلة: "npm: command not found"
- تأكد من تثبيت Node.js بشكل صحيح
- أعد تشغيل Terminal بعد التثبيت

### مشكلة: "Port already in use"
- تأكد من عدم تشغيل تطبيق آخر على نفس المنفذ
- غير المنفذ في ملف `.env` للخادم الخلفي

### مشكلة: "Cannot connect to backend"
- تأكد من تشغيل الخادم الخلفي أولاً
- تحقق من أن الخادم يعمل على المنفذ 5000

## 📝 الخطوات التالية

1. ✅ تثبيت Node.js
2. ✅ تشغيل التطبيق
3. 🔄 إضافة ميزات جديدة
4. 🔄 ربط قاعدة بيانات
5. 🔄 إضافة نظام المصادقة
6. 🔄 نشر التطبيق
