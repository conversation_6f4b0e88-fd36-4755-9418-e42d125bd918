.App {
  text-align: center;
  direction: rtl;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

h1 {
  margin-bottom: 20px;
  color: #61dafb;
}

.message {
  font-size: 1.2em;
  margin: 20px 0;
  padding: 15px;
  background-color: #4CAF50;
  color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.features {
  margin-top: 30px;
  text-align: right;
}

.features h2 {
  color: #61dafb;
  margin-bottom: 15px;
}

.features ul {
  list-style: none;
  padding: 0;
}

.features li {
  margin: 10px 0;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  font-size: 0.9em;
}

@media (max-width: 768px) {
  .App-header {
    font-size: calc(8px + 2vmin);
    padding: 15px;
  }
  
  .features {
    text-align: center;
  }
}
