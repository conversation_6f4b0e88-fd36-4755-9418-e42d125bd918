import { useState } from 'react'

function ApiTest() {
  const [users, setUsers] = useState([])
  const [newUser, setNewUser] = useState({ name: '', email: '' })
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  // جلب البيانات من API
  const fetchUsers = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/data')
      const data = await response.json()
      setUsers(data.data)
      setMessage(data.message)
    } catch (error) {
      setMessage('خطأ في جلب البيانات: ' + error.message)
    }
    setLoading(false)
  }

  // إضافة مستخدم جديد
  const addUser = async (e) => {
    e.preventDefault()
    if (!newUser.name || !newUser.email) {
      setMessage('يرجى إدخال الاسم والبريد الإلكتروني')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newUser),
      })
      
      const data = await response.json()
      
      if (response.ok) {
        setMessage(data.message)
        setNewUser({ name: '', email: '' })
        // إعادة جلب البيانات
        fetchUsers()
      } else {
        setMessage(data.error || 'حدث خطأ')
      }
    } catch (error) {
      setMessage('خطأ في إضافة المستخدم: ' + error.message)
    }
    setLoading(false)
  }

  return (
    <div className="api-test">
      <h2>🧪 اختبار API</h2>
      
      {message && (
        <div className={`message ${message.includes('خطأ') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      <div className="section">
        <h3>جلب البيانات</h3>
        <button onClick={fetchUsers} disabled={loading}>
          {loading ? 'جاري التحميل...' : 'جلب المستخدمين'}
        </button>
        
        {users.length > 0 && (
          <div className="users-list">
            <h4>قائمة المستخدمين:</h4>
            {users.map(user => (
              <div key={user.id} className="user-card">
                <strong>{user.name}</strong> - {user.email}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="section">
        <h3>إضافة مستخدم جديد</h3>
        <form onSubmit={addUser} className="user-form">
          <input
            type="text"
            placeholder="الاسم"
            value={newUser.name}
            onChange={(e) => setNewUser({...newUser, name: e.target.value})}
            disabled={loading}
          />
          <input
            type="email"
            placeholder="البريد الإلكتروني"
            value={newUser.email}
            onChange={(e) => setNewUser({...newUser, email: e.target.value})}
            disabled={loading}
          />
          <button type="submit" disabled={loading}>
            {loading ? 'جاري الإضافة...' : 'إضافة مستخدم'}
          </button>
        </form>
      </div>

      <style jsx>{`
        .api-test {
          margin: 20px 0;
          padding: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
          background-color: rgba(255, 255, 255, 0.05);
        }

        .section {
          margin: 20px 0;
          padding: 15px;
          border: 1px solid #444;
          border-radius: 5px;
        }

        .message {
          padding: 10px;
          margin: 10px 0;
          border-radius: 5px;
          text-align: center;
        }

        .message.success {
          background-color: #4CAF50;
          color: white;
        }

        .message.error {
          background-color: #f44336;
          color: white;
        }

        .users-list {
          margin-top: 15px;
        }

        .user-card {
          padding: 8px;
          margin: 5px 0;
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
          font-size: 0.9em;
        }

        .user-form {
          display: flex;
          flex-direction: column;
          gap: 10px;
          max-width: 300px;
        }

        .user-form input {
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
        }

        .user-form button {
          padding: 10px;
          background-color: #61dafb;
          color: #282c34;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: bold;
        }

        .user-form button:hover:not(:disabled) {
          background-color: #21a9c7;
        }

        .user-form button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        button {
          padding: 10px 20px;
          background-color: #61dafb;
          color: #282c34;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: bold;
          margin: 5px;
        }

        button:hover:not(:disabled) {
          background-color: #21a9c7;
        }

        button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      `}</style>
    </div>
  )
}

export default ApiTest
